{"test_config": {"webhook_url": "https://edubot.schoolpro.kz/webhook", "test_type": "burst", "timestamp": "20250614_204338"}, "results": [{"success": true, "response_time": 0.3703124523162842, "status_code": 200, "timestamp": 1749915818.1065383, "user_id": 1000000, "error": null}, {"success": true, "response_time": 0.37401652336120605, "status_code": 200, "timestamp": 1749915818.111244, "user_id": 1000001, "error": null}, {"success": true, "response_time": 0.382080078125, "status_code": 200, "timestamp": 1749915818.1193075, "user_id": 1000002, "error": null}, {"success": true, "response_time": 0.36524343490600586, "status_code": 200, "timestamp": 1749915818.1024709, "user_id": 1000003, "error": null}, {"success": true, "response_time": 0.36731934547424316, "status_code": 200, "timestamp": 1749915818.1045468, "user_id": 1000004, "error": null}, {"success": true, "response_time": 0.38301753997802734, "status_code": 200, "timestamp": 1749915818.120245, "user_id": 1000005, "error": null}, {"success": true, "response_time": 0.37299299240112305, "status_code": 200, "timestamp": 1749915818.1102204, "user_id": 1000006, "error": null}, {"success": true, "response_time": 0.36524343490600586, "status_code": 200, "timestamp": 1749915818.1024709, "user_id": 1000007, "error": null}, {"success": true, "response_time": 0.3693108558654785, "status_code": 200, "timestamp": 1749915818.1065383, "user_id": 1000008, "error": null}, {"success": true, "response_time": 0.37299299240112305, "status_code": 200, "timestamp": 1749915818.1102204, "user_id": 1000009, "error": null}, {"success": true, "response_time": 0.3663606643676758, "status_code": 200, "timestamp": 1749915818.103588, "user_id": 1000010, "error": null}, {"success": true, "response_time": 0.3683662414550781, "status_code": 200, "timestamp": 1749915818.1102204, "user_id": 1000011, "error": null}, {"success": true, "response_time": 0.36061668395996094, "status_code": 200, "timestamp": 1749915818.1024709, "user_id": 1000012, "error": null}, {"success": true, "response_time": 0.36269259452819824, "status_code": 200, "timestamp": 1749915818.1045468, "user_id": 1000013, "error": null}, {"success": true, "response_time": 0.3783907890319824, "status_code": 200, "timestamp": 1749915818.120245, "user_id": 1000014, "error": null}, {"success": true, "response_time": 0.38179850578308105, "status_code": 200, "timestamp": 1749915818.1236527, "user_id": 1000015, "error": null}, {"success": true, "response_time": 0.37352967262268066, "status_code": 200, "timestamp": 1749915818.1153839, "user_id": 1000016, "error": null}, {"success": true, "response_time": 0.3828001022338867, "status_code": 200, "timestamp": 1749915818.1246543, "user_id": 1000017, "error": null}, {"success": true, "response_time": 0.3683662414550781, "status_code": 200, "timestamp": 1749915818.1102204, "user_id": 1000018, "error": null}, {"success": true, "response_time": 0.367506742477417, "status_code": 200, "timestamp": 1749915818.1102204, "user_id": 1000019, "error": null}, {"success": true, "response_time": 0.47842884063720703, "status_code": 200, "timestamp": 1749915818.2211425, "user_id": 1000020, "error": null}, {"success": true, "response_time": 0.47933387756347656, "status_code": 200, "timestamp": 1749915818.2220476, "user_id": 1000021, "error": null}, {"success": true, "response_time": 0.48035717010498047, "status_code": 200, "timestamp": 1749915818.2230709, "user_id": 1000022, "error": null}, {"success": true, "response_time": 0.47933387756347656, "status_code": 200, "timestamp": 1749915818.2220476, "user_id": 1000023, "error": null}, {"success": true, "response_time": 0.48035717010498047, "status_code": 200, "timestamp": 1749915818.2230709, "user_id": 1000024, "error": null}, {"success": true, "response_time": 0.482708215713501, "status_code": 200, "timestamp": 1749915818.225422, "user_id": 1000025, "error": null}, {"success": true, "response_time": 0.4816899299621582, "status_code": 200, "timestamp": 1749915818.2244036, "user_id": 1000026, "error": null}, {"success": true, "response_time": 0.4837071895599365, "status_code": 200, "timestamp": 1749915818.2264209, "user_id": 1000027, "error": null}, {"success": true, "response_time": 0.482708215713501, "status_code": 200, "timestamp": 1749915818.225422, "user_id": 1000028, "error": null}, {"success": true, "response_time": 0.48470568656921387, "status_code": 200, "timestamp": 1749915818.2274194, "user_id": 1000029, "error": null}, {"success": true, "response_time": 0.4857645034790039, "status_code": 200, "timestamp": 1749915818.2284782, "user_id": 1000030, "error": null}, {"success": true, "response_time": 0.48470568656921387, "status_code": 200, "timestamp": 1749915818.2274194, "user_id": 1000031, "error": null}, {"success": true, "response_time": 0.4857645034790039, "status_code": 200, "timestamp": 1749915818.2284782, "user_id": 1000032, "error": null}, {"success": true, "response_time": 0.4872782230377197, "status_code": 200, "timestamp": 1749915818.229992, "user_id": 1000033, "error": null}, {"success": true, "response_time": 0.49243760108947754, "status_code": 200, "timestamp": 1749915818.2351513, "user_id": 1000034, "error": null}, {"success": true, "response_time": 0.4965333938598633, "status_code": 200, "timestamp": 1749915818.239247, "user_id": 1000035, "error": null}, {"success": true, "response_time": 0.49744582176208496, "status_code": 200, "timestamp": 1749915818.2401595, "user_id": 1000036, "error": null}, {"success": true, "response_time": 0.4955313205718994, "status_code": 200, "timestamp": 1749915818.239247, "user_id": 1000037, "error": null}, {"success": true, "response_time": 0.4990828037261963, "status_code": 200, "timestamp": 1749915818.2427986, "user_id": 1000038, "error": null}, {"success": true, "response_time": 0.5003223419189453, "status_code": 200, "timestamp": 1749915818.244038, "user_id": 1000039, "error": null}, {"success": true, "response_time": 0.5555381774902344, "status_code": 200, "timestamp": 1749915818.299254, "user_id": 1000040, "error": null}, {"success": true, "response_time": 0.5580437183380127, "status_code": 200, "timestamp": 1749915818.3017595, "user_id": 1000041, "error": null}, {"success": true, "response_time": 0.5580437183380127, "status_code": 200, "timestamp": 1749915818.3017595, "user_id": 1000042, "error": null}, {"success": true, "response_time": 0.5580437183380127, "status_code": 200, "timestamp": 1749915818.3017595, "user_id": 1000043, "error": null}, {"success": true, "response_time": 0.559640645980835, "status_code": 200, "timestamp": 1749915818.3033564, "user_id": 1000044, "error": null}, {"success": true, "response_time": 0.5601603984832764, "status_code": 200, "timestamp": 1749915818.3038762, "user_id": 1000045, "error": null}, {"success": true, "response_time": 0.5632390975952148, "status_code": 200, "timestamp": 1749915818.3069549, "user_id": 1000046, "error": null}, {"success": true, "response_time": 0.5611815452575684, "status_code": 200, "timestamp": 1749915818.3048973, "user_id": 1000047, "error": null}, {"success": true, "response_time": 0.5632390975952148, "status_code": 200, "timestamp": 1749915818.3069549, "user_id": 1000048, "error": null}, {"success": true, "response_time": 0.5632390975952148, "status_code": 200, "timestamp": 1749915818.3069549, "user_id": 1000049, "error": null}], "system_metrics": [{"timestamp": 1749915817.8566966, "cpu_percent": 10.8, "memory_percent": 82.3, "memory_used_mb": 13256.578125, "network_sent_mb": 0.01710033416748047, "network_recv_mb": 0.004464149475097656}]}